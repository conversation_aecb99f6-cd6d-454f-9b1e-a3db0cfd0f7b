{"name": "simile-frontend", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.68", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "axios": "^1.6.5", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.21.1", "react-scripts": "5.0.1", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src --ext .ts,.tsx", "typecheck": "tsc --noEmit", "test:e2e": "playwright test", "test:e2e:headed": "playwright test --headed", "test:e2e:ui": "playwright test --ui", "test:e2e:report": "playwright show-report", "test:e2e:install": "playwright install", "test:e2e:debug": "playwright test --debug", "test:e2e:smoke": "./e2e/run-e2e-smoke.sh", "test:e2e:entities": "./e2e/run-e2e-functional.sh --area=entities", "test:e2e:connections": "./e2e/run-e2e-functional.sh --area=connections", "test:e2e:comparisons": "../run-e2e-functional.sh --area=comparisons", "test:e2e:navigation": "../run-e2e-functional.sh --area=navigation", "test:e2e:error-handling": "../run-e2e-functional.sh --area=error-handling", "test:e2e:integration": "../run-e2e-functional.sh --area=integration", "test:e2e:performance": "../run-e2e-performance.sh", "test:e2e:full": "../run-e2e-full.sh", "test:e2e:functional": "../run-e2e-functional.sh"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@playwright/test": "^1.53.1", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "eslint": "^8.56.0", "eslint-config-react-app": "^7.0.1"}}