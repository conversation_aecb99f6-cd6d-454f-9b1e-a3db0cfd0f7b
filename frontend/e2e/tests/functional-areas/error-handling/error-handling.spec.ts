import { test, expect } from '@playwright/test';
import { EntityManagerPage, ConnectionManagerPage, ComparisonManagerPage } from '../fixtures/page-objects';
import { TestHelpers } from '../utils/helpers';

test.describe('Error Handling and Edge Cases', () => {
  let entityPage: EntityManagerPage;
  let connectionPage: ConnectionManagerPage;
  let comparisonPage: ComparisonManagerPage;
  let helpers: TestHelpers;

  test.beforeEach(async ({ page }) => {
    helpers = new TestHelpers(page);
    await helpers.setupDialogHandler();
    helpers.setupConsoleErrorTracking(); // Phase B.2: Enhanced error tracking
    
    entityPage = new EntityManagerPage(page);
    connectionPage = new ConnectionManagerPage(page);
    comparisonPage = new ComparisonManagerPage(page);
  });

  test.afterEach(async ({ page }) => {
    await helpers.cleanupTestEntities();
    await helpers.restoreNetwork(); // Phase B.2: Reset network simulation
    helpers.clearConsoleErrors(); // Phase B.2: Clear error tracking
  });

  test('should handle API error gracefully', async ({ page }) => {
    // Test with backend potentially unavailable
    const isBackendHealthy = await helpers.checkBackendHealth();
    
    if (isBackendHealthy) {
      // Skip this test if backend is running normally
      test.skip();
      return;
    }
    
    await entityPage.goto('/entities');
    await helpers.waitForAppReady();
    
    // Try to create an entity when backend is down
    await entityPage.clickCreateNew();
    await entityPage.nameInput.fill('Test Entity');
    await entityPage.submitButton.click();
    
    // Should show error message
    await expect(entityPage.errorMessage).toBeVisible({ timeout: 10000 });
  });

  test('should handle network timeout gracefully', async ({ page }) => {
    // Phase B.2: Improved network simulation - timeout instead of long delay
    await page.route('**/api/v1/entities', async route => {
      if (route.request().method() === 'POST') {
        // Abort the request to simulate network timeout
        await route.abort('timedout');
      } else {
        await route.continue();
      }
    });
    
    await entityPage.goto('/entities');
    await helpers.waitForAppReady();
    
    // Use enhanced error handling method
    const timeoutEntityName = helpers.generateUniqueEntityName('Timeout');
    const result = await entityPage.submitFormWithErrorHandling(timeoutEntityName);
    
    // Should detect timeout error
    expect(result.success).toBe(false);
    expect(result.error).toBeTruthy();
    console.log(`Network timeout handled: ${result.error}`);
  });

  test('should handle malformed API responses', async ({ page }) => {
    // Intercept API calls and return malformed responses
    await page.route('**/api/v1/entities', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: '{"invalid": "json"}'  // Missing expected fields
      });
    });
    
    await entityPage.goto('/entities');
    
    // Should handle malformed response gracefully
    await expect(page.locator('body')).toBeVisible();
    // Application shouldn't crash
  });

  test('should handle server 500 errors', async ({ page }) => {
    // Intercept API calls and return server errors
    await page.route('**/api/v1/entities', async route => {
      if (route.request().method() === 'POST') {
        await route.fulfill({
          status: 500,
          contentType: 'application/json',
          body: '{"error": "Internal server error"}'
        });
      } else {
        await route.continue();
      }
    });
    
    await entityPage.goto('/entities');
    await entityPage.clickCreateNew();
    await entityPage.nameInput.fill('Server Error Test');
    await entityPage.submitButton.click();
    
    // Should show error message for server error
    await expect(entityPage.errorMessage).toBeVisible();
  });

  test('should handle 404 errors for missing entities', async ({ page }) => {
    await comparisonPage.goto('/');
    await helpers.waitForAppReady();
    
    // Try to compare non-existent entities with unique names
    const nonExistent1 = helpers.generateUniqueEntityName('NonExistent1');
    const nonExistent2 = helpers.generateUniqueEntityName('NonExistent2');
    
    try {
      await comparisonPage.compareEntities(nonExistent1, nonExistent2);
      
      // Wait for error message with timeout
      const errorText = await comparisonPage.waitForError(10000);
      expect(errorText).toBeTruthy();
      expect(errorText?.toLowerCase()).toMatch(/not found|does not exist|invalid|missing/);
      console.log(`404 error handled correctly: ${errorText}`);
    } catch (error) {
      console.log(`404 test failed with exception: ${error}`);
      throw error;
    }
  });

  test('should handle browser console errors', async ({ page }) => {
    await entityPage.goto('/entities');
    await helpers.waitForAppReady();
    
    // Clear any existing console errors
    helpers.clearConsoleErrors();
    
    // Perform normal operations
    const consoleTestName = helpers.generateUniqueEntityName('Console');
    const result = await entityPage.submitFormWithErrorHandling(consoleTestName);
    
    // Check for JavaScript errors in console
    const consoleErrors = helpers.getConsoleErrors();
    const criticalErrors = consoleErrors.filter(error => 
      !error.includes('Warning') && // Filter out React warnings
      !error.includes('DevTools') && // Filter out DevTools messages
      !error.includes('Download the React DevTools') && // Filter out React DevTools
      error.toLowerCase().includes('error')
    );
    
    console.log(`Console errors detected: ${criticalErrors.length}`);
    if (criticalErrors.length > 0) {
      console.log('Critical errors:', criticalErrors);
    }
    
    // Allow up to 2 minor console errors (React development mode often has some)
    expect(criticalErrors.length).toBeLessThanOrEqual(2);
  });

  test('should handle empty/whitespace-only inputs', async ({ page }) => {
    await entityPage.goto('/entities');
    await helpers.waitForAppReady();
    
    const testCases = ['', '   ', '\t\n  '];
    
    for (const testInput of testCases) {
      await entityPage.clickCreateNew();
      
      // Use enhanced error handling
      const result = await entityPage.submitFormWithErrorHandling(testInput);
      
      // Should fail with validation error or disabled submit
      expect(result.success).toBe(false);
      console.log(`Empty/whitespace test for '${testInput}': ${result.error}`);
      
      // Clear any error state
      if (await entityPage.entityForm.isVisible()) {
        await entityPage.cancelForm();
      }
      await entityPage.clearError();
    }
  });

  test('should handle very long entity names', async ({ page }) => {
    await entityPage.goto('/entities');
    await entityPage.clickCreateNew();
    
    // Test extremely long name (beyond normal limits)
    const veryLongName = 'A'.repeat(1000);
    await entityPage.nameInput.fill(veryLongName);
    await entityPage.submitButton.click();
    
    // Should show validation error
    await expect(entityPage.errorMessage).toBeVisible();
  });

  test('should handle special Unicode characters', async ({ page }) => {
    const unicodeNames = [
      'Test Entity 🚀',    // Emoji
      'Tést Entïty',       // Accented characters  
      'Тест Сущность',     // Cyrillic
      '测试实体',           // Chinese
      'テストエンティティ'   // Japanese
    ];
    
    await entityPage.goto('/entities');
    await helpers.waitForAppReady();
    
    for (const name of unicodeNames) {
      console.log(`Testing Unicode name: ${name}`);
      
      // Use enhanced error handling method
      const result = await entityPage.submitFormWithErrorHandling(name);
      
      if (result.success) {
        console.log(`Unicode name '${name}' accepted`);
        // Cleanup created entity
        await helpers.cleanupTestEntities();
      } else {
        console.log(`Unicode name '${name}' rejected: ${result.error}`);
      }
      
      // Clear any error state
      await entityPage.clearError();
    }
  });

  test('should handle concurrent user actions', async ({ page }) => {
    await entityPage.goto('/entities');
    await helpers.waitForAppReady();
    
    const entityName = helpers.generateUniqueEntityName('Concurrent');
    
    // Simulate rapid clicking
    await entityPage.clickCreateNew();
    await entityPage.nameInput.fill(entityName);
    
    // Click submit multiple times rapidly
    await Promise.all([
      entityPage.submitButton.click(),
      entityPage.submitButton.click(),
      entityPage.submitButton.click()
    ]);
    
    // Should handle gracefully without creating duplicates
    await page.waitForTimeout(1000);
    
    // Check that only one entity was created
    const entityCount = await page.locator(`:text("${entityName}")`).count();
    expect(entityCount).toBeLessThanOrEqual(1);
  });

  test('should handle page refresh during form submission', async ({ page }) => {
    await entityPage.goto('/entities');
    await entityPage.clickCreateNew();
    await entityPage.nameInput.fill('Refresh Test');
    
    // Start submission and immediately refresh
    const submitPromise = entityPage.submitButton.click();
    const refreshPromise = page.reload();
    
    await Promise.all([submitPromise, refreshPromise]);
    
    // Page should load correctly after refresh
    await helpers.waitForAppReady();
    await expect(page.locator('h2:has-text("Entity Management")')).toBeVisible();
  });

  test('should handle browser back during form editing', async ({ page }) => {
    await entityPage.goto('/entities');
    await entityPage.clickCreateNew();
    await entityPage.nameInput.fill('Back Test');
    
    // Navigate back while form is open
    await page.goBack();
    
    // Navigate forward again
    await page.goForward();
    
    // Page should be in a consistent state
    await helpers.waitForAppReady();
    await expect(page.locator('h2:has-text("Entity Management")')).toBeVisible();
  });

  test('should handle extremely large numbers in connections', async ({ page }) => {
    const testSuffix = Math.random().toString(36).substring(2, 8).replace(/[0-9]/g, '').toUpperCase();
    const entity1 = `Large Num A ${testSuffix}`;
    const entity2 = `Large Num B ${testSuffix}`;
    
    // Create entities
    await entityPage.goto('/entities');
    await entityPage.createEntity(entity1);
    await entityPage.createEntity(entity2);
    
    await connectionPage.goto('/connections');
    
    // Test very large number
    await connectionPage.createConnection(entity1, entity2, '999999999.9');
    
    // Should either accept or show appropriate validation error
    await Promise.race([
      connectionPage.errorMessage.waitFor({ state: 'visible' }),
      page.locator(`:text("${entity1} → ${entity2}")`).waitFor({ state: 'visible' })
    ]);
  });

  test('should handle scientific notation in connections', async ({ page }) => {
    const testSuffix = Math.random().toString(36).substring(2, 8).replace(/[0-9]/g, '').toUpperCase();
    const entity1 = `Sci Not A ${testSuffix}`;
    const entity2 = `Sci Not B ${testSuffix}`;
    
    // Create entities
    await entityPage.goto('/entities');
    await entityPage.createEntity(entity1);
    await entityPage.createEntity(entity2);
    
    await connectionPage.goto('/connections');
    
    // Test scientific notation
    const scientificValues = ['1e5', '1.5e-3', '2.5E+2'];
    
    for (const value of scientificValues) {
      await connectionPage.clickCreateNew();
      await connectionPage.fromEntityInput.fill(entity1);
      await connectionPage.toEntityInput.fill(entity2);
      await connectionPage.multiplierInput.fill(value);
      await connectionPage.submitButton.click();
      
      // Should handle appropriately based on validation rules
      await Promise.race([
        connectionPage.errorMessage.waitFor({ state: 'visible' }),
        connectionPage.connectionForm.waitFor({ state: 'hidden' })
      ]);
      
      if (await connectionPage.connectionForm.isVisible()) {
        await connectionPage.cancelForm();
      }
    }
  });
});