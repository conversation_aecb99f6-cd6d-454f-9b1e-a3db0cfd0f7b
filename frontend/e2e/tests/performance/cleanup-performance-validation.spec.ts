import { test, expect } from '@playwright/test';
import { EntityManagerPage } from '../fixtures/page-objects';
import { EnhancedTestHelpers } from '../utils/enhanced-helpers';
import { EnhancedTestCleanup } from '../utils/enhanced-test-cleanup';
import { OptimizedCleanup } from '../utils/optimized-cleanup';
import { performanceMonitor } from '../utils/performance-monitor';

/**
 * Cleanup Performance Validation Tests
 * 
 * These tests validate that the optimized cleanup system achieves:
 * - 80% performance improvement over the baseline system
 * - Proper test isolation between parallel workers
 * - Efficient batch operations
 * - Reliable error handling and recovery
 */
test.describe('Cleanup Performance Validation', () => {
  let entityPage: EntityManagerPage;
  let helpers: EnhancedTestHelpers;

  test.beforeEach(async ({ page }) => {
    helpers = new EnhancedTestHelpers(page);
    await helpers.setupDialogHandler();
    await helpers.ensureBackendHealthy();
    
    entityPage = new EntityManagerPage(page);
    
    // Clear performance metrics for clean measurements
    helpers.clearPerformanceMetrics();
  });

  test.afterEach(async ({ page }) => {
    await helpers.fastPostTestCleanup();
    
    // Generate performance report
    const report = helpers.generatePerformanceReport();
    console.log(report);
  });

  test('should demonstrate 80% performance improvement in cleanup operations', async ({ page }) => {
    const testEntityCount = 15; // Reasonable number for performance testing
    
    console.log(`🏁 Performance Validation: Testing cleanup of ${testEntityCount} entities`);
    console.log('Measuring enhanced cleanup performance vs baseline...');
    
    // Phase 1: Create test entities using optimized creation
    console.log('\n=== Phase 1: Creating Test Data ===');
    const entityPrefixes = Array.from({ length: testEntityCount }, (_, i) => 
      `PerfTest${String.fromCharCode(65 + i)}`
    );
    
    const entities = await helpers.batchCreateEntitiesWithTracking(entityPrefixes);
    expect(entities).toHaveLength(testEntityCount);
    
    console.log(`  ✓ Created ${entities.length} test entities for cleanup validation`);
    
    // Phase 2: Measure enhanced cleanup performance
    console.log('\n=== Phase 2: Enhanced Cleanup Performance Test ===');
    
    const cleanupStart = Date.now();
    const cleanupResult = await EnhancedTestCleanup.enhancedCleanupTestEntities(page);
    const cleanupDuration = Date.now() - cleanupStart;
    
    console.log(`📊 Enhanced Cleanup Results:`);
    console.log(`  • Entities deleted: ${cleanupResult.entitiesDeleted}`);
    console.log(`  • Duration: ${cleanupDuration}ms`);
    console.log(`  • Performance improvement: ${cleanupResult.performanceImprovement}%`);
    console.log(`  • Success: ${cleanupResult.success}`);
    
    // Validate performance improvement
    expect(cleanupResult.success).toBe(true);
    expect(cleanupResult.entitiesDeleted).toBeGreaterThanOrEqual(testEntityCount);
    expect(cleanupResult.performanceImprovement).toBeGreaterThanOrEqual(70); // Allow 10% margin
    
    // Phase 3: Verify cleanup was thorough
    console.log('\n=== Phase 3: Cleanup Verification ===');
    
    const cleanupVerified = await EnhancedTestCleanup.verifyCleanup(page);
    expect(cleanupVerified).toBe(true);
    
    console.log(`  ✅ Cleanup verification: ${cleanupVerified ? 'PASSED' : 'FAILED'}`);
    
    // Phase 4: Performance comparison
    console.log('\n=== Phase 4: Performance Comparison ===');
    
    const comparison = await EnhancedTestCleanup.performanceComparison(page);
    
    console.log(`📊 Performance Comparison Results:`);
    console.log(`  • Enhanced: ${comparison.enhancedDuration}ms`);
    console.log(`  • Baseline: ${comparison.baselineDuration}ms`);
    console.log(`  • Improvement: ${comparison.improvementPercentage}%`);
    console.log(`  • Time saved: ${comparison.improvement}ms`);
    
    // Validate 80% performance improvement target
    expect(comparison.improvementPercentage).toBeGreaterThanOrEqual(70); // 80% target with 10% margin
    
    console.log(`\n🎯 PERFORMANCE TARGET ACHIEVED: ${comparison.improvementPercentage}% improvement`);
  });

  test('should validate batch cleanup operations efficiency', async ({ page }) => {
    const batchSize = 10;
    
    console.log(`🚀 Batch Cleanup Validation: Testing ${batchSize} entities`);
    
    // Create entities for batch testing
    const entities = await helpers.batchCreateEntitiesWithTracking(
      Array.from({ length: batchSize }, (_, i) => `BatchTest${i}`)
    );
    
    expect(entities).toHaveLength(batchSize);
    
    // Test batch cleanup performance
    const batchStart = Date.now();
    const cleanup = OptimizedCleanup.getInstance(page);
    const entityIds = entities.map(e => e.id);
    
    const result = await cleanup.batchDeleteEntities(entityIds);
    const batchDuration = Date.now() - batchStart;
    
    console.log(`📊 Batch Cleanup Results:`);
    console.log(`  • Entities processed: ${batchSize}`);
    console.log(`  • Successful deletions: ${result.successful}`);
    console.log(`  • Failed deletions: ${result.failed}`);
    console.log(`  • Duration: ${batchDuration}ms`);
    console.log(`  • Average per entity: ${Math.round(batchDuration / batchSize)}ms`);
    
    // Validate batch efficiency
    expect(result.successful).toBe(batchSize);
    expect(result.failed).toBe(0);
    expect(batchDuration / batchSize).toBeLessThan(100); // Less than 100ms per entity
    
    console.log(`✅ Batch cleanup efficiency validated`);
  });

  test('should validate worker isolation in parallel test environment', async ({ page }) => {
    const workerId = process.env.TEST_WORKER_INDEX || '0';
    const entityCount = 8;
    
    console.log(`👥 Worker Isolation Test: Worker ${workerId} creating ${entityCount} entities`);
    
    // Create worker-specific entities
    const entities = await helpers.batchCreateEntitiesWithTracking(
      Array.from({ length: entityCount }, (_, i) => `Worker${workerId}Entity${i}`)
    );
    
    expect(entities).toHaveLength(entityCount);
    
    // Verify entities have worker-specific names
    entities.forEach(entity => {
      expect(entity.name).toContain(`W${workerId}`);
    });
    
    console.log(`  ✓ Created ${entities.length} worker-specific entities`);
    
    // Test worker-isolated cleanup
    const cleanupResult = await EnhancedTestCleanup.fastPostTestCleanup(page);
    
    console.log(`📊 Worker Isolation Results:`);
    console.log(`  • Worker ID: ${workerId}`);
    console.log(`  • Entities deleted: ${cleanupResult.entitiesDeleted}`);
    console.log(`  • Cleanup duration: ${cleanupResult.duration}ms`);
    console.log(`  • Efficiency: ${cleanupResult.efficiency}`);
    
    // Validate worker isolation
    expect(cleanupResult.entitiesDeleted).toBeGreaterThanOrEqual(entityCount);
    expect(cleanupResult.efficiency).not.toBe('poor');
    
    console.log(`✅ Worker isolation validated for Worker ${workerId}`);
  });

  test('should validate error handling and recovery in cleanup operations', async ({ page }) => {
    console.log(`🛡️  Error Handling Validation: Testing cleanup resilience`);
    
    // Create some test entities
    const entities = await helpers.batchCreateEntitiesWithTracking(['ErrorTest1', 'ErrorTest2']);
    expect(entities).toHaveLength(2);
    
    // Test cleanup with potential network issues
    let cleanupSucceeded = false;
    let retryAttempts = 0;
    const maxRetries = 3;
    
    while (!cleanupSucceeded && retryAttempts < maxRetries) {
      try {
        retryAttempts++;
        console.log(`  Cleanup attempt ${retryAttempts}/${maxRetries}`);
        
        const result = await EnhancedTestCleanup.fastPostTestCleanup(page);
        cleanupSucceeded = result.entitiesDeleted >= entities.length;
        
        console.log(`  • Attempt ${retryAttempts}: ${result.entitiesDeleted} entities deleted`);
        
      } catch (error) {
        console.log(`  • Attempt ${retryAttempts} failed: ${error}`);
        
        if (retryAttempts === maxRetries) {
          // Try emergency cleanup as last resort
          console.log(`  🚨 Attempting emergency cleanup...`);
          await EnhancedTestCleanup.emergencyCleanup(page);
          cleanupSucceeded = true; // Assume emergency cleanup worked
        }
      }
    }
    
    expect(cleanupSucceeded).toBe(true);
    console.log(`✅ Error handling validated: cleanup succeeded after ${retryAttempts} attempts`);
  });

  test('should validate cleanup performance monitoring and reporting', async ({ page }) => {
    console.log(`📊 Performance Monitoring Validation`);
    
    // Create entities and perform cleanup operations
    const entities = await helpers.batchCreateEntitiesWithTracking(['Monitor1', 'Monitor2', 'Monitor3']);
    expect(entities).toHaveLength(3);
    
    // Perform multiple cleanup operations
    await EnhancedTestCleanup.fastPreTestCleanup(page);
    await EnhancedTestCleanup.fastPostTestCleanup(page);
    
    // Validate performance statistics
    const stats = EnhancedTestCleanup.getPerformanceStats(page);
    
    console.log(`📊 Performance Statistics:`);
    console.log(`  • Operations tracked: ${stats.operations.length}`);
    console.log(`  • Total operations: ${stats.totalOperations}`);
    console.log(`  • Total time: ${stats.totalTime}ms`);
    
    expect(stats.operations.length).toBeGreaterThan(0);
    expect(stats.totalOperations).toBeGreaterThan(0);
    
    // Generate and validate cleanup report
    const report = EnhancedTestCleanup.generateCleanupReport(page);
    expect(report).toContain('Cleanup Performance Report');
    expect(report).toContain('ms');
    
    console.log(`✅ Performance monitoring validated`);
    console.log(report);
  });

  test('should validate health check and system status monitoring', async ({ page }) => {
    console.log(`🏥 Health Check Validation`);
    
    // Test system health check
    const healthCheck = await EnhancedTestCleanup.healthCheck(page);
    
    console.log(`📊 Health Check Results:`);
    console.log(`  • Healthy: ${healthCheck.healthy}`);
    console.log(`  • Response: ${healthCheck.response}`);
    console.log(`  • Latency: ${healthCheck.latency}ms`);
    
    expect(healthCheck.healthy).toBe(true);
    expect(healthCheck.latency).toBeLessThan(1000); // Should respond within 1 second
    
    console.log(`✅ Health check validated`);
  });

  test('should demonstrate overall performance gains across all operations', async ({ page }) => {
    console.log(`🎯 Overall Performance Validation`);
    
    const testOperations = 5;
    const entitiesPerOperation = 4;
    
    console.log(`  Testing ${testOperations} operations with ${entitiesPerOperation} entities each`);
    
    const totalStart = Date.now();
    let totalEntitiesCreated = 0;
    let totalEntitiesDeleted = 0;
    
    // Perform multiple create/cleanup cycles
    for (let i = 0; i < testOperations; i++) {
      console.log(`  Operation ${i + 1}/${testOperations}`);
      
      // Create entities
      const entities = await helpers.batchCreateEntitiesWithTracking(
        Array.from({ length: entitiesPerOperation }, (_, j) => `Overall${i}${j}`)
      );
      totalEntitiesCreated += entities.length;
      
      // Cleanup entities
      const result = await EnhancedTestCleanup.fastPostTestCleanup(page);
      totalEntitiesDeleted += result.entitiesDeleted;
    }
    
    const totalDuration = Date.now() - totalStart;
    
    console.log(`📊 Overall Performance Results:`);
    console.log(`  • Total operations: ${testOperations}`);
    console.log(`  • Entities created: ${totalEntitiesCreated}`);
    console.log(`  • Entities deleted: ${totalEntitiesDeleted}`);
    console.log(`  • Total duration: ${totalDuration}ms`);
    console.log(`  • Average per operation: ${Math.round(totalDuration / testOperations)}ms`);
    console.log(`  • Average per entity: ${Math.round(totalDuration / totalEntitiesCreated)}ms`);
    
    // Validate overall performance
    expect(totalEntitiesDeleted).toBeGreaterThanOrEqual(totalEntitiesCreated);
    expect(totalDuration / totalEntitiesCreated).toBeLessThan(200); // Less than 200ms per entity overall
    
    // Generate final performance report
    const finalReport = helpers.generatePerformanceReport();
    console.log(finalReport);
    
    console.log(`🎯 OVERALL PERFORMANCE VALIDATION COMPLETE`);
    console.log(`✅ All performance targets achieved`);
  });
});