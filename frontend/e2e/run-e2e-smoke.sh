#!/bin/bash

# SIMILE E2E Smoke Tests
# Run critical path tests only (~5-10 tests, <2 minutes)
# These tests cover the most essential functionality that must work
# for the application to be considered functional.

echo "🚀 Running SIMILE E2E Smoke Tests"
echo "=================================="
echo "Target: Critical path functionality"
echo "Expected duration: <2 minutes"
echo ""

# Set environment variables for smoke testing
export TEST_TYPE="smoke"
export TEST_TIMEOUT_MULTIPLIER="1.0"

# Run smoke tests with specific configuration
npx playwright test \
  --grep="@smoke" \
  --reporter=line \
  --workers=2 \
  --timeout=30000 \
  --retries=1

# Capture the exit code from the test run
TEST_RESULT=$?

echo ""
if [ $TEST_RESULT -eq 0 ]; then
  echo "✅ Smoke tests PASSED"
  echo "All critical functionality is working correctly."
else
  echo "❌ Smoke tests FAILED"
  echo "CRITICAL: The application has fundamental issues that need immediate attention."
  echo "Exit code: $TEST_RESULT"
fi

# Exit with the actual test result
exit $TEST_RESULT
