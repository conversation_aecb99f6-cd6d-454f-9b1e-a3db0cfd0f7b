#!/bin/bash

# SIMILE E2E Performance Tests
# Run performance and stress tests
# These tests are run with single worker to avoid interference

echo "🚀 Running SIMILE E2E Performance Tests"
echo "======================================="
echo "Target: Performance benchmarks and stress tests"
echo "Expected duration: 5-10 minutes"
echo "Workers: 1 (to avoid interference)"
echo ""

# Set environment variables for performance testing
export TEST_TYPE="performance"
export TEST_TIMEOUT_MULTIPLIER="2.0"

# Run performance tests with single worker
npx playwright test \
  --grep="@performance" \
  performance/ \
  --reporter=line \
  --workers=1 \
  --timeout=120000 \
  --retries=0 \
  --max-failures=5

echo ""
echo "✅ Performance tests completed"
echo "Check the output above for performance metrics and benchmarks."
